﻿//HintName: RightData.gen.cs
// Generated from Samples/Right
// Collected from ManyFiles

namespace GqlPlusTests;

public class SamplesRightYellowData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File108",
    "File213",
    "File3",
    "File318",
    "File423",
  ];

  public SamplesRightYellowData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Yellow";
  public const string Collected = "ManyFiles";
}

public class SamplesRightRedInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File113",
    "File218",
    "File323",
    "File428",
    "File8",
  ];

  public SamplesRightRedInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Red/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesRightPurpleValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File118",
    "File13",
    "File223",
    "File328",
    "File433",
  ];

  public SamplesRightPurpleValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Purple/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesRightGreenData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File123",
    "File18",
    "File228",
    "File333",
    "File438",
  ];

  public SamplesRightGreenData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Green";
  public const string Collected = "ManyFiles";
}

public class SamplesRightOrangeInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File128",
    "File23",
    "File233",
    "File338",
    "File443",
  ];

  public SamplesRightOrangeInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Orange/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesRightValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File133",
    "File238",
    "File28",
    "File343",
    "File448",
  ];

  public SamplesRightValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesRightBlueData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File138",
    "File243",
    "File33",
    "File348",
    "File453",
  ];

  public SamplesRightBlueData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Blue";
  public const string Collected = "ManyFiles";
}

public class SamplesRightYellowInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File143",
    "File248",
    "File353",
    "File38",
    "File458",
  ];

  public SamplesRightYellowInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Yellow/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesRightRedValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File148",
    "File253",
    "File358",
    "File43",
    "File463",
  ];

  public SamplesRightRedValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Red/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesRightPurpleData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File153",
    "File258",
    "File363",
    "File468",
    "File48",
  ];

  public SamplesRightPurpleData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Purple";
  public const string Collected = "ManyFiles";
}

public class SamplesRightGreenInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File158",
    "File263",
    "File368",
    "File473",
    "File53",
  ];

  public SamplesRightGreenInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Green/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesRightOrangeValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File163",
    "File268",
    "File373",
    "File478",
    "File58",
  ];

  public SamplesRightOrangeValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Orange/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesRightData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File168",
    "File273",
    "File378",
    "File483",
    "File63",
  ];

  public SamplesRightData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/";
  public const string Collected = "ManyFiles";
}

public class SamplesRightBlueInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File173",
    "File278",
    "File383",
    "File488",
    "File68",
  ];

  public SamplesRightBlueInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Blue/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesRightYellowValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File178",
    "File283",
    "File388",
    "File493",
    "File73",
  ];

  public SamplesRightYellowValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Yellow/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesRightRedData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File183",
    "File288",
    "File393",
    "File498",
    "File78",
  ];

  public SamplesRightRedData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Red";
  public const string Collected = "ManyFiles";
}

public class SamplesRightPurpleInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File188",
    "File293",
    "File398",
    "File83",
  ];

  public SamplesRightPurpleInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Purple/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesRightGreenValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File193",
    "File298",
    "File403",
    "File88",
  ];

  public SamplesRightGreenValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Green/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesRightOrangeData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File198",
    "File303",
    "File408",
    "File93",
  ];

  public SamplesRightOrangeData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Orange";
  public const string Collected = "ManyFiles";
}

public class SamplesRightInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File203",
    "File308",
    "File413",
    "File98",
  ];

  public SamplesRightInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesRightBlueValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File103",
    "File208",
    "File313",
    "File418",
  ];

  public SamplesRightBlueValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Right/Blue/Valid";
  public const string Collected = "ManyFiles";
}
