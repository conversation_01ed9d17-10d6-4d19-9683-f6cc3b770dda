﻿//HintName: CentreData.gen.cs
// Generated from Samples/Centre
// Collected from ManyFiles

namespace GqlPlusTests;

public class SamplesCentreOrangeInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File107",
    "File2",
    "File212",
    "File317",
    "File422",
  ];

  public SamplesCentreOrangeInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Orange/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File112",
    "File217",
    "File322",
    "File427",
    "File7",
  ];

  public SamplesCentreValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreBlueData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File117",
    "File12",
    "File222",
    "File327",
    "File432",
  ];

  public SamplesCentreBlueData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Blue";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreYellowInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File122",
    "File17",
    "File227",
    "File332",
    "File437",
  ];

  public SamplesCentreYellowInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Yellow/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreRedValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File127",
    "File22",
    "File232",
    "File337",
    "File442",
  ];

  public SamplesCentreRedValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Red/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesCentrePurpleData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File132",
    "File237",
    "File27",
    "File342",
    "File447",
  ];

  public SamplesCentrePurpleData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Purple";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreGreenInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File137",
    "File242",
    "File32",
    "File347",
    "File452",
  ];

  public SamplesCentreGreenInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Green/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreOrangeValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File142",
    "File247",
    "File352",
    "File37",
    "File457",
  ];

  public SamplesCentreOrangeValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Orange/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File147",
    "File252",
    "File357",
    "File42",
    "File462",
  ];

  public SamplesCentreData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreBlueInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File152",
    "File257",
    "File362",
    "File467",
    "File47",
  ];

  public SamplesCentreBlueInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Blue/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreYellowValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File157",
    "File262",
    "File367",
    "File472",
    "File52",
  ];

  public SamplesCentreYellowValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Yellow/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreRedData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File162",
    "File267",
    "File372",
    "File477",
    "File57",
  ];

  public SamplesCentreRedData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Red";
  public const string Collected = "ManyFiles";
}

public class SamplesCentrePurpleInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File167",
    "File272",
    "File377",
    "File482",
    "File62",
  ];

  public SamplesCentrePurpleInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Purple/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreGreenValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File172",
    "File277",
    "File382",
    "File487",
    "File67",
  ];

  public SamplesCentreGreenValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Green/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreOrangeData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File177",
    "File282",
    "File387",
    "File492",
    "File72",
  ];

  public SamplesCentreOrangeData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Orange";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File182",
    "File287",
    "File392",
    "File497",
    "File77",
  ];

  public SamplesCentreInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreBlueValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File187",
    "File292",
    "File397",
    "File82",
  ];

  public SamplesCentreBlueValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Blue/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreYellowData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File192",
    "File297",
    "File402",
    "File87",
  ];

  public SamplesCentreYellowData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Yellow";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreRedInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File197",
    "File302",
    "File407",
    "File92",
  ];

  public SamplesCentreRedInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Red/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesCentrePurpleValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File202",
    "File307",
    "File412",
    "File97",
  ];

  public SamplesCentrePurpleValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Purple/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesCentreGreenData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File102",
    "File207",
    "File312",
    "File417",
  ];

  public SamplesCentreGreenData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Centre/Green";
  public const string Collected = "ManyFiles";
}
