﻿//HintName: BottomData.gen.cs
// Generated from Samples/Bottom
// Collected from ManyFiles

namespace GqlPlusTests;

public class SamplesBottomGreenValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File109",
    "File214",
    "File319",
    "File4",
    "File424",
  ];

  public SamplesBottomGreenValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Green/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomOrangeData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File114",
    "File219",
    "File324",
    "File429",
    "File9",
  ];

  public SamplesBottomOrangeData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Orange";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File119",
    "File14",
    "File224",
    "File329",
    "File434",
  ];

  public SamplesBottomInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomBlueValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File124",
    "File19",
    "File229",
    "File334",
    "File439",
  ];

  public SamplesBottomBlueValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Blue/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomYellowData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File129",
    "File234",
    "File24",
    "File339",
    "File444",
  ];

  public SamplesBottomYellowData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Yellow";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomRedInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File134",
    "File239",
    "File29",
    "File344",
    "File449",
  ];

  public SamplesBottomRedInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Red/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomPurpleValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File139",
    "File244",
    "File34",
    "File349",
    "File454",
  ];

  public SamplesBottomPurpleValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Purple/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomGreenData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File144",
    "File249",
    "File354",
    "File39",
    "File459",
  ];

  public SamplesBottomGreenData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Green";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomOrangeInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File149",
    "File254",
    "File359",
    "File44",
    "File464",
  ];

  public SamplesBottomOrangeInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Orange/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File154",
    "File259",
    "File364",
    "File469",
    "File49",
  ];

  public SamplesBottomValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomBlueData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File159",
    "File264",
    "File369",
    "File474",
    "File54",
  ];

  public SamplesBottomBlueData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Blue";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomYellowInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File164",
    "File269",
    "File374",
    "File479",
    "File59",
  ];

  public SamplesBottomYellowInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Yellow/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomRedValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File169",
    "File274",
    "File379",
    "File484",
    "File64",
  ];

  public SamplesBottomRedValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Red/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomPurpleData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File174",
    "File279",
    "File384",
    "File489",
    "File69",
  ];

  public SamplesBottomPurpleData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Purple";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomGreenInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File179",
    "File284",
    "File389",
    "File494",
    "File74",
  ];

  public SamplesBottomGreenInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Green/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomOrangeValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File184",
    "File289",
    "File394",
    "File499",
    "File79",
  ];

  public SamplesBottomOrangeValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Orange/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File189",
    "File294",
    "File399",
    "File84",
  ];

  public SamplesBottomData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomBlueInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File194",
    "File299",
    "File404",
    "File89",
  ];

  public SamplesBottomBlueInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Blue/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomYellowValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File199",
    "File304",
    "File409",
    "File94",
  ];

  public SamplesBottomYellowValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Yellow/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomRedData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File204",
    "File309",
    "File414",
    "File99",
  ];

  public SamplesBottomRedData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Red";
  public const string Collected = "ManyFiles";
}

public class SamplesBottomPurpleInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File104",
    "File209",
    "File314",
    "File419",
  ];

  public SamplesBottomPurpleInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Bottom/Purple/InValid";
  public const string Collected = "ManyFiles";
}
