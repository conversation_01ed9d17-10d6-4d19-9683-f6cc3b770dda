﻿//HintName: LeftData.gen.cs
// Generated from Samples/Left
// Collected from ManyFiles

namespace GqlPlusTests;

public class SamplesLeftRedValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File1",
    "File106",
    "File211",
    "File316",
    "File421",
  ];

  public SamplesLeftRedValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Red/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftPurpleData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File111",
    "File216",
    "File321",
    "File426",
    "File6",
  ];

  public SamplesLeftPurpleData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Purple";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftGreenInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File11",
    "File116",
    "File221",
    "File326",
    "File431",
  ];

  public SamplesLeftGreenInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Green/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftOrangeValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File121",
    "File16",
    "File226",
    "File331",
    "File436",
  ];

  public SamplesLeftOrangeValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Orange/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File126",
    "File21",
    "File231",
    "File336",
    "File441",
  ];

  public SamplesLeftData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftBlueInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File131",
    "File236",
    "File26",
    "File341",
    "File446",
  ];

  public SamplesLeftBlueInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Blue/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftYellowValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File136",
    "File241",
    "File31",
    "File346",
    "File451",
  ];

  public SamplesLeftYellowValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Yellow/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftRedData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File141",
    "File246",
    "File351",
    "File36",
    "File456",
  ];

  public SamplesLeftRedData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Red";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftPurpleInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File146",
    "File251",
    "File356",
    "File41",
    "File461",
  ];

  public SamplesLeftPurpleInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Purple/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftGreenValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File151",
    "File256",
    "File361",
    "File46",
    "File466",
  ];

  public SamplesLeftGreenValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Green/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftOrangeData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File156",
    "File261",
    "File366",
    "File471",
    "File51",
  ];

  public SamplesLeftOrangeData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Orange";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File161",
    "File266",
    "File371",
    "File476",
    "File56",
  ];

  public SamplesLeftInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftBlueValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File166",
    "File271",
    "File376",
    "File481",
    "File61",
  ];

  public SamplesLeftBlueValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Blue/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftYellowData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File171",
    "File276",
    "File381",
    "File486",
    "File66",
  ];

  public SamplesLeftYellowData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Yellow";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftRedInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File176",
    "File281",
    "File386",
    "File491",
    "File71",
  ];

  public SamplesLeftRedInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Red/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftPurpleValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File181",
    "File286",
    "File391",
    "File496",
    "File76",
  ];

  public SamplesLeftPurpleValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Purple/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftGreenData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File186",
    "File291",
    "File396",
    "File81",
  ];

  public SamplesLeftGreenData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Green";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftOrangeInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File191",
    "File296",
    "File401",
    "File86",
  ];

  public SamplesLeftOrangeInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Orange/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File196",
    "File301",
    "File406",
    "File91",
  ];

  public SamplesLeftValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftBlueData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File201",
    "File306",
    "File411",
    "File96",
  ];

  public SamplesLeftBlueData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Blue";
  public const string Collected = "ManyFiles";
}

public class SamplesLeftYellowInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File101",
    "File206",
    "File311",
    "File416",
  ];

  public SamplesLeftYellowInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Left/Yellow/InValid";
  public const string Collected = "ManyFiles";
}
