﻿//HintName: TopData.gen.cs
// Generated from Samples/Top
// Collected from ManyFiles

namespace GqlPlusTests;

public class SamplesTopData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File0",
    "File105",
    "File210",
    "File315",
    "File420",
  ];

  public SamplesTopData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/";
  public const string Collected = "ManyFiles";
}

public class SamplesTopBlueInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File110",
    "File215",
    "File320",
    "File425",
    "File5",
  ];

  public SamplesTopBlueInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Blue/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesTopYellowValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File10",
    "File115",
    "File220",
    "File325",
    "File430",
  ];

  public SamplesTopYellowValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Yellow/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesTopRedData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File120",
    "File15",
    "File225",
    "File330",
    "File435",
  ];

  public SamplesTopRedData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Red";
  public const string Collected = "ManyFiles";
}

public class SamplesTopPurpleInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File125",
    "File20",
    "File230",
    "File335",
    "File440",
  ];

  public SamplesTopPurpleInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Purple/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesTopGreenValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File130",
    "File235",
    "File25",
    "File340",
    "File445",
  ];

  public SamplesTopGreenValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Green/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesTopOrangeData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File135",
    "File240",
    "File30",
    "File345",
    "File450",
  ];

  public SamplesTopOrangeData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Orange";
  public const string Collected = "ManyFiles";
}

public class SamplesTopInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File140",
    "File245",
    "File35",
    "File350",
    "File455",
  ];

  public SamplesTopInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesTopBlueValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File145",
    "File250",
    "File355",
    "File40",
    "File460",
  ];

  public SamplesTopBlueValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Blue/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesTopYellowData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File150",
    "File255",
    "File360",
    "File45",
    "File465",
  ];

  public SamplesTopYellowData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Yellow";
  public const string Collected = "ManyFiles";
}

public class SamplesTopRedInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File155",
    "File260",
    "File365",
    "File470",
    "File50",
  ];

  public SamplesTopRedInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Red/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesTopPurpleValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File160",
    "File265",
    "File370",
    "File475",
    "File55",
  ];

  public SamplesTopPurpleValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Purple/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesTopGreenData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File165",
    "File270",
    "File375",
    "File480",
    "File60",
  ];

  public SamplesTopGreenData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Green";
  public const string Collected = "ManyFiles";
}

public class SamplesTopOrangeInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File170",
    "File275",
    "File380",
    "File485",
    "File65",
  ];

  public SamplesTopOrangeInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Orange/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesTopValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File175",
    "File280",
    "File385",
    "File490",
    "File70",
  ];

  public SamplesTopValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesTopBlueData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File180",
    "File285",
    "File390",
    "File495",
    "File75",
  ];

  public SamplesTopBlueData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Blue";
  public const string Collected = "ManyFiles";
}

public class SamplesTopYellowInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File185",
    "File290",
    "File395",
    "File80",
  ];

  public SamplesTopYellowInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Yellow/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesTopRedValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File190",
    "File295",
    "File400",
    "File85",
  ];

  public SamplesTopRedValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Red/Valid";
  public const string Collected = "ManyFiles";
}

public class SamplesTopPurpleData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File195",
    "File300",
    "File405",
    "File90",
  ];

  public SamplesTopPurpleData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Purple";
  public const string Collected = "ManyFiles";
}

public class SamplesTopGreenInValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File200",
    "File305",
    "File410",
    "File95",
  ];

  public SamplesTopGreenInValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Green/InValid";
  public const string Collected = "ManyFiles";
}

public class SamplesTopOrangeValidData
  : TheoryData<string>
{
  public static readonly string[] Strings = [
    "File100",
    "File205",
    "File310",
    "File415",
  ];

  public SamplesTopOrangeValidData()
  {
    foreach (string s in Strings) Add(s);
  }

  public const string From = "Samples/Top/Orange/Valid";
  public const string Collected = "ManyFiles";
}
